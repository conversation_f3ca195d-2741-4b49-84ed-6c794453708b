import { useState, useRef } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";
import SecondaryButton from "@/Components/SecondaryButton";
import AppToggleSwitchComponent from "@/Components/App/AppToggleSwitchComponent";
import { MdDelete, MdOutlineUpdate, MdWarning, MdAutorenew } from "react-icons/md";
import { getEventValue } from "@/Util/TargetInputEvent";
import useOutsideClick from "@/Util/useOutsideClick";
import InputError from "@/Components/InputError";
import setDefaultDateFormat from "../../../Util/setDefaultDateFormat";

export default function RenewItems({
    index,
    domain,
    handleChangeItem,
    onDeleteItem,
    renewalPrice,
    gracePeriod,
    penalty = 0
}) {
    const [yearToggle, setYearToggle] = useState(false);
    const ref = useRef();
    const MAX_YEARS = 10;
    const YEAR_BY_SECS = 31557600;

    useOutsideClick(ref, () => setYearToggle(false));

    const calculateMaxExpiry = (selectedExpiry) => {
        var todayEpoch = Date.parse(new Date());
        var createdEpoch = Date.parse(domain.created_at);
        var addToCreatedYears = parseInt(new Date(domain.created_at).getFullYear()) + parseInt(MAX_YEARS);
        var expiredNumYears = Math.floor(((todayEpoch - createdEpoch) / 1000.0 / YEAR_BY_SECS));
        var addToMaxExpiryYear = addToCreatedYears + expiredNumYears;
        var createdMaxTermEpoch = new Date(domain.created_at).setFullYear(addToMaxExpiryYear);
        var selectedExpiryEpoch = Date.parse(selectedExpiry);
        var isMax = (selectedExpiryEpoch >= createdMaxTermEpoch) ? true : false;

        return isMax;
    }

    const getRenewDate = (year) => {
        const domainExpiry = new Date(domain.expiry);

        var calculatedExpiry = new Date(domainExpiry.getFullYear() + year, domainExpiry.getMonth(), domainExpiry.getDate());
        return calculatedExpiry;
    }

    const [expiry, setExpiry] = useState(getRenewDate(domain.year_length))

    const onHandleChange = (event) => {
        domain[event.target.name] = getEventValue(event);
        handleChangeItem(index, domain, event.target.name, getEventValue(event));
    };

    const onHandleChangeYear = (year) => {
        setExpiry(getRenewDate(year));
        domain.year_length = year;
        setYearToggle(!yearToggle)
        handleChangeItem(index, domain, "year_length", year, calculateMaxExpiry(getRenewDate(year)));
    };

    const handleAutoRenewalToggle = (newState) => {
        domain.auto_renew = newState;
        handleChangeItem(index, domain, "auto_renew", newState);
    };

    const priceFormat = (year) => {
        const totalPrice = year * renewalPrice;
        return parseFloat(totalPrice).toFixed(2);
    }

    const yearList = [...Array(10).keys()].map((i) => (
        <button
            key={"yearlist" + i}
            className="px-5 py-1 hover:bg-gray-100"
            onClick={() => onHandleChangeYear(i + 1)}
        >
            {i + 1} {i > 0 ? " years" : "  year"}
        </button>
    ));

    return (
        <div className={`" space-y-2" }`}>
            <div className="flex items-center justify-between pb-2 mb-4 text-gray-600 border-b border-gray-200">
                <span className="flex items-center space-x-2">
                    <label className="text-2xl">{domain.name}</label>
                    {/* {penalty > 0 && <MdWarning size={22} className="text-danger" />} */}
                </span>
                <button onClick={() => onDeleteItem(domain.id, index)}>
                    <MdDelete className="p-1 text-3xl transition duration-150 rounded-full cursor-pointer hover:bg-black hover:bg-opacity-20" />
                </button>
            </div>
            {penalty > 0 && (
                <div ref={ref} className="relative flex items-center justify-between mb-1 hover:bg-gray-50">
                    <div className="flex items-center">
                        <MdWarning size={19} className="text-danger" />
                        <span className="ml-2 text-md text-danger">
                            {`Late renewal fee was applied as this domain is over ${gracePeriod} days expired.`}
                        </span>
                    </div>
                    <div className={"inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 tracking-widest shadow-sm text-xs font-semibold cursor-default"}>
                        ${parseFloat(penalty).toFixed(2)}
                    </div>
                </div>
            )}
            <div ref={ref} className="relative flex items-center justify-between hover:bg-gray-50">
                <div className="flex items-center">
                    <MdOutlineUpdate className="text-lg " />
                    <span className="ml-2 text-gray-600 text-md">
                        Renew until {setDefaultDateFormat(expiry)}
                    </span>
                </div>
                <SecondaryButton name="show_year" onClick={() => setYearToggle(!yearToggle)}>
                    <span>
                        ${priceFormat(domain.year_length)} / {domain.year_length}{" "}
                        {domain.year_length > 1 ? "years" : "year"}
                    </span>
                    <svg
                        className="ml-2 -mr-0.5 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                    >
                        <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                        />
                    </svg>
                </SecondaryButton>
                <DropDownContainer show={yearToggle} className="right-0 -bottom-[3rem] bg-white top-10 h-36 overflow-y-scroll">
                    {yearList}
                </DropDownContainer>
            </div>

            {/* Auto-renewal toggle */}
            <div ref={ref} className="relative flex items-center justify-between py-2 hover:bg-gray-50">
                <div className="flex items-center">
                    <MdAutorenew className="text-lg text-blue-500" />
                    <div className="ml-2">
                        <span className="text-gray-600 text-md">Enable Auto-Renewal</span>
                        <p className="text-xs text-gray-500">Automatically renew this domain before it expires</p>
                    </div>
                </div>
                <div className="origin-center scale-75">
                    <AppToggleSwitchComponent
                        isChecked={domain.auto_renew ?? true}
                        onChangeEvent={handleAutoRenewalToggle}
                    />
                </div>
            </div>

            {calculateMaxExpiry(expiry) && (<div
                className="relative flex items-center justify-between hover:bg-gray-50"
            >
                <div className="flex items-center">
                    <InputError
                        message="Maximum registration period exceeded. Please remove domain or lower the year value selected."
                        className="w-full ml-2 text-md"
                    />
                </div>
            </div>)}
        </div>
    );
}
