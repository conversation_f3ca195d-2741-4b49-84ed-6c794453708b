//* PACKAGES
import React, { useState } from "react";
import { router, usePage, Link } from "@inertiajs/react";

//* ICONS
import { MdKeyboardBackspace } from "react-icons/md";

//* COMPONENTS
import AppVerificationPromptGroup from "@/Components/App/AppVerificationPromptGroupComponent";
import PrimaryButton from "@/Components/PrimaryButton";
import RedemptionItems from "./RedemptionItems.jsx";
import Checkbox from "@/Components/Checkbox";
import EmptyCart from "@/Components/Domain/Cart/EmptyCart";

//* PARTIALS
//...

//* STATE
//...

//* UTILS
import { getEventValue } from "@/Util/TargetInputEvent";
import UtilCheckIfHasSecuredTransaction from "@/Util/UtilCheckIfHasSecuredTransaction";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function RedemptionContainer(
    {
        data,
        settings
    }
) {
    //! PACKAGE
    const user = usePage().props.auth.user;

    //! VARIABLES
    const shouldVerifyUser = UtilCheckIfHasSecuredTransaction('domainRedemption');
    const comPrice = parseFloat(settings.renewal_fees.com.price);
    const netPrice = parseFloat(settings.renewal_fees.net.price);
    const orgPrice = parseFloat(settings.renewal_fees.org.price);
    const redemptionFeeValue = parseFloat(settings.redemption_fee.value);

    //! STATES
    const [stateShowVerificationPrompt, setStateShowVerificationPrompt] = useState(false);
    const [isAgree, setIsAgree] = useState(false);
    const [domainItems, setDomainItems] = useState(data);

    const getRenewalPrice = (tld) => {
        switch (parseInt(tld)) {
            case 1: return comPrice;
            case 2: return netPrice;
            case 3: return orgPrice;
            default: return 0;
        }
    }

    const onRedemptionTotal = (data) => {
        var total = 0;
        var renewalPrice = 0;

        data.map((domain) => {
            renewalPrice = getRenewalPrice(domain.tld_id);
            const yearLength = domain.year_length || 1;
            // Renewal fee × years + redemption fee per domain
            total += (renewalPrice * yearLength) + redemptionFeeValue;
        })

        return total;
    }

    const [redemptionTotal, setRedemptionTotal] = useState(onRedemptionTotal(data))

    //! USE EFFECTS
    //...

    //! FUNCTIONS
    function handleOnSubmit() {
        router.post(
            route("domain.redeem.pay"),
            {
                user_id: user.id,
                domains: domainItems,
            }, {
            replace: true,
            preserveState: false,
        }
        );
    }

    const onHandleChangeAgree = (event) => {
        setIsAgree(getEventValue(event));
    };

    const getTotal = (redemptionData) => {
        var rTotal = onRedemptionTotal(redemptionData);
        setRedemptionTotal(rTotal);
    }

    const onHandleChangeItem = (index, domain, column, value) => {
        let domains = [...domainItems]
        domains[index] = domain
        setDomainItems(domains);
        getTotal(domains);
    }

    const onDeleteItem = (id, index) => {
        if (domainItems != null) {
            const temp = domainItems.filter((item) => item.id !== id);
            setDomainItems(temp);
            getTotal(temp);
        }
    }

    if (domainItems.length == 0)
        return (
            <EmptyCart
                message={'No domains selected'}
                link={'domain'}
                isSearch={false}
            />
        );

    return (
        <div className="mx-auto container max-w-[900px] mt-20 flex flex-col space-y-4">
            <AppVerificationPromptGroup
                isShow={stateShowVerificationPrompt}
                onClose={() => setStateShowVerificationPrompt(false)}
                onSubmitSuccess={handleOnSubmit}
                onSubmitError={() => alert('error')}
            />
            <div className="flex items-center space-x-4 text-gray-700 text-lg font-semibold">
                <Link
                    href={route("domain")}
                >
                    <MdKeyboardBackspace className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer" />
                </Link>
                <span className="text-inherit">
                    Redemption Summary
                </span>
                <span className="text-gray-500">{domainItems.length} {(domainItems.length == 1) ? "item" : "items"}</span>
            </div>
            <div className="flex flex-col space-y-8 pt-8">
                {domainItems.map((domain, index) => {
                    return (
                        <RedemptionItems
                            key={"rd-" + domain.id}
                            domain={domain}
                            index={index}
                            handleChangeItem={onHandleChangeItem}
                            onDeleteItem={onDeleteItem}
                            renewalPrice={getRenewalPrice(domain.tld_id)}
                            redemptionFee={redemptionFeeValue}
                        />
                    );
                })}
                <div className="flex items-center justify-between space-y-2 text-lg text-gray-700 font-semibold">
                    <span className=" text-inherit">Redemption Total</span>
                    <span className=" text-inherit">${redemptionTotal.toFixed(2)}</span>
                </div>
                <div className="flex items-center justify-end flex-col space-y-4">
                    <label className="flex items-center">
                        <Checkbox
                            name="is_agree"
                            value="is_agree"
                            checked={isAgree}
                            handleChange={onHandleChangeAgree}
                        />
                        <span className="ml-2 text-sm text-gray-600 text-center">
                            By clicking "Restore" you confirm that you have
                            read the StrangeDomains' &nbsp;
                            <a className="underline text-sm text-link" href={route("refund.policy")} target="_blank">
                                Refund Policy.
                            </a>
                        </span>

                    </label>
                    <PrimaryButton
                        className="w-full"
                        onClick={
                            (e) => {
                                e.preventDefault();

                                if (shouldVerifyUser == true) {
                                    setStateShowVerificationPrompt(true);
                                }
                                else {
                                    handleOnSubmit()
                                }
                            }
                        }
                        processing={!isAgree}
                    >
                        Restore
                    </PrimaryButton>
                </div>
            </div>
        </div>
    );
}
