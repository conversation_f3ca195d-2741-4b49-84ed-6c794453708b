<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\UpdateDomainsTableEvent;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainJobTypes;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Util\Constant\QueueConnection;
use App\Util\Helper\Domain\DomainParser;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Services\DomainFees;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Services\StripeLimiter;
use Illuminate\Support\Facades\Auth;

class RedemptionDomainService
{
    use UserLoggerTrait;

    private int $dispatchDelayInSeconds = 30;
    private int $dispatchThirtySeconds = 30;

    public static function instance(): self
    {
        return new self;
    }

    public function getData(array $request): array
    {
        StripeLimiter::instance()->checkAttempt();

        $domains = $request['domains'];
        $renewalFees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::RENEW);
        $redemptionFee = DomainFees::instance()->getFee(FeeType::REDEMPTION);
        $settings = [
            'renewal_fees' => $renewalFees,
            'redemption_fee' => $redemptionFee
        ];

        return [
            'domains' => $domains,
            'settings' => $settings,
        ];
    }

    public function getRedemptionPaymentData(array $request): array
    {
        $domains = $request['domains'];
        $other_fees = PaymentFeeService::getOtherRedemptionFees($domains);
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($other_fees['bill_total'] ?? 0);
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($stripeFeeObj['gross_amount'] ?? $other_fees['bill_total']);
        $setupIntents = PaymentIntentProvider::instance()->create($payment);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        $result = RegistryAccountBalanceService::checkRegistryBalance($domains, $other_fees, FeeType::REDEMPTION);
        if ($result['error']) {
            throw new \Exception($result['message']);
        }

        // Add settings data for frontend compatibility
        $renewalFees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::RENEW);
        $redemptionFee = DomainFees::instance()->getFee(FeeType::REDEMPTION);
        $settings = [
            'renewal_fees' => $renewalFees,
            'redemption_fee' => $redemptionFee
        ];

        // Debug logging
        \Log::info('RedemptionDomainService::getRedemptionPaymentData - Settings:', $settings);

        return [
            'domains' => $domains,
            'other_fees' => $other_fees,
            'settings' => $settings,
            'secret' => $setupIntents->client_secret ?? '',
            'promise' => config('stripe.publishable_key'),
            'intent' => $setupIntents->id ?? '',
            'account_credit_balance' => $accountCredit->running_balance ?? 0,
            'stripeFeeObj' => $stripeFeeObj,
        ];
    }

    public function update(array $request): string
    {
        StripeLimiter::instance()->clearAttempt();

        $paymentPayload = $this->createPaymentPayload($request);
        $invoiceId = PaymentSummaryService::instance()->createPayment($paymentPayload, $this->getUserId(), $request['payment_summary_type'], $request['payment_service_type']);
        $refundDetails = PaymentReimbursementService::instance()->createRefundDetails(PaymentSummaryType::PAYMENT_REIMBURSEMENT, $request['payment_service_type'], $invoiceId);

        $this->redeemDomainJob($request['domains'], $refundDetails);
        ScheduleDomainExpiryNotice::dispatch($this->getUserId())->delay($this->dispatchDelayInSeconds);

        return $invoiceId;
    }

    public function checkAndCreditRegistryBalance(array $request)
    {
        $registryId_balance = $this->checkRegistryBalance($request, FeeType::REDEMPTION);
        $this->creditRegistryAccountBalance($registryId_balance);
    }

    private function checkRegistryBalance(array $data, string $type): array
    {
        $result = RegistryAccountBalanceService::checkRegistryBalance($data['domains'], $data['other_fees'], $type, $data['intent'] ?? '');
        if ($result['error']) {
            throw new \Exception($result['message']);
        }
        return $result['registryBalance'];
    }

    private function creditRegistryAccountBalance(array $registryBalance): void
    {
        foreach ($registryBalance as $balance) {
            RegistryAccountBalanceService::credit($balance['balance'], $balance['amount'], RegistryTransactionType::SUB_FUND, RegistryTransactionType::DOMAIN_REDEMPTION);
        }
    }

    private function setDomainsToProcess(object $domains): void
    {
        $domainIds = $domains->pluck('id')->toArray();
        DomainService::instance()->updateDomainStatus($domainIds, DomainStatus::IN_PROCESS);
        UpdateDomainsTableEvent::dispatch($this->getUserId());
    }

    private function redeemDomainJob(array $domains, array $refundData)
    {
        $domainObj = collect(json_decode(json_encode($domains), false));
        $this->setDomainsToProcess($domainObj);
        $defaultPayload = $this->getDefaultPayload();

        $count = 0;
        foreach ($domainObj as $domain) {
            JobDispatchService::instance()->redemptionEppDispatch(
                $this->getJobPayload($domain, $defaultPayload, $refundData),
                $this->dispatchThirtySeconds + $count
            );
            $count += 1;
        }
    }

    private function getDefaultPayload()
    {
        return [
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
        ];
    }

    private function getJobPayload(object $domain, array $default, array $refundData)
    {
        $registry = DomainParser::getRegistryName($domain->name);
        $registeredDomain = [
            'id' => $domain->registered_domain_id,
            'name' => $domain->name,
        ];

        return [
            JobPayloadKeys::DOMAIN => $domain,
            JobPayloadKeys::REGISTERED_DOMAIN => json_decode(json_encode($registeredDomain), false),
            JobPayloadKeys::USER_ID => $default[JobPayloadKeys::USER_ID],
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::EMAIL => $default[JobPayloadKeys::EMAIL],
            JobPayloadKeys::UPDATE_TYPE => DomainJobTypes::UPDATE_REDEMPTION,
            JobPayloadKeys::QUEUE_TYPE => QueueConnection::DOMAIN_REDEMPTION,
            JobPayloadKeys::REFUND_DATA => $refundData,
        ];
    }

    private function getUserId(): int
    {
        return Auth::user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        return Auth::user()->email ?? 'Unauthorized';
    }

    private function createPaymentPayload(array $request)
    {
        $otherFees = json_decode(json_encode($request['other_fees']), true);
        $registeredDomains = collect(json_decode(json_encode($request['domains']), false));

        $invoice = PaymentInvoiceService::instance()->createInvoicePayload(FeeType::REDEMPTION, $this->getUserId(), $otherFees, $request['intent'] ?? null);
        $nodeInvoice = PaymentInvoiceService::instance()->createNodeInvoicePayload(FeeType::REDEMPTION, $registeredDomains, $otherFees);

        return PaymentInvoiceService::instance()->createPaymentPayload($invoice, $nodeInvoice);
    }
}
