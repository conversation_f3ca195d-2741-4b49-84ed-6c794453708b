import React, { useEffect, useState } from "react";
import UserLayout from "@/Layouts/UserLayout";
import RenewalContainer from "../../Components/Domain/Renewal/RenewalContainer";
import { useForm, usePage, router} from "@inertiajs/react";


export default function Renewal({ domains, settings }) {
    const user = usePage().props.auth.user;
    const { setError } = useForm({
        user_id: user.id,
    });

    useEffect(
        () => {
            window.history.pushState(null, '', window.location.href);
            window.onpopstate = () => {
                window.history.replaceState(null, '', router.get('domain'));
                window.location.reload();
            };
        },
        []
    );

    return (
        <UserLayout hideNav={true} postRouteName={'domain.renew.confirm'}>
            <RenewalContainer
                data={domains}
                settings={settings}
            />
        </UserLayout>
    );
}
